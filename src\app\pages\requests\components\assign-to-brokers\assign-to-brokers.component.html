<div class="mb-5 mt-0">
  <app-broker-title></app-broker-title>
</div>

<div class="card mb-5 mb-xl-5" [class.rtl-layout]="translationService.getCurrentLanguage() === 'ar'">
  <div class="card-body pt-3 pb-0">
    <div class="d-flex flex-wrap flex-sm-nowrap mb-1">
      <div class="flex-grow-1">
        <div class="d-flex justify-content-between align-items-start flex-wrap mb-2"
          [class.rtl-header]="translationService.getCurrentLanguage() === 'ar'">
          <div class="d-flex my-4">
            <h1 class="text-dark-blue fs-2 fw-bolder me-1"
              [class.rtl-title]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('REQUESTS') }}
            </h1>
          </div>

          <div class="d-flex my-4 mt-0">
            <form data-kt-search-element="form" class="w-300px position-relative mb-3" autocomplete="off"
              [class.rtl-search]="translationService.getCurrentLanguage() === 'ar'">
              <app-keenicon name="magnifier"
                class="fs-2 fs-lg-1 text-gray-500 position-absolute top-50 translate-middle-y ms-3"
                *ngIf="translationService.getCurrentLanguage() !== 'ar'"></app-keenicon>
              <input type="text" class="form-control form-control-flush bg-light border rounded-pill"
                [class.ps-10]="translationService.getCurrentLanguage() !== 'ar'"
                [class.ps-3]="translationService.getCurrentLanguage() === 'ar'"
                [class.rtl-input]="translationService.getCurrentLanguage() === 'ar'" name="search" value=""
                placeholder="{{ getTranslatedText('SEARCH') }}" data-kt-search-element="input" />
            </form>
          </div>

          <div class="d-flex my-4">
            <a class="btn btn-sm btn-light-dark-blue cursor-pointer"
              [class.me-3]="translationService.getCurrentLanguage() !== 'ar'"
              [class.ms-3]="translationService.getCurrentLanguage() === 'ar'"
              [class.rtl-button]="translationService.getCurrentLanguage() === 'ar'">
              <i class="fa-solid fa-filter"></i> {{ getTranslatedText('FILTER') }}
            </a>
            <a class="btn btn-sm btn-light-dark-blue cursor-pointer"
              [class.me-3]="translationService.getCurrentLanguage() !== 'ar'"
              [class.ms-3]="translationService.getCurrentLanguage() === 'ar'"
              [class.rtl-button]="translationService.getCurrentLanguage() === 'ar'">
              <i class="fa-solid fa-arrow-down-wide-short"></i> {{ getTranslatedText('SORT') }}
            </a>
          </div>
        </div>
      </div>
    </div>

    <div class="table-responsive">
      <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3 mt-5">
        <thead>
          <tr class="fw-bold bg-light-dark-blue text-dark-blue me-1 ms-1"
            [class.rtl-table-header]="translationService.getCurrentLanguage() === 'ar'">
            <th class="w-25px rounded-start" [class.ps-4]="translationService.getCurrentLanguage() !== 'ar'"
              [class.pe-4]="translationService.getCurrentLanguage() === 'ar'">
              <div class="form-check form-check-sm form-check-custom form-check-solid">
                <input class="form-check-input" type="checkbox" value="1" data-kt-check="true"
                  data-kt-check-target=".widget-13-check" [(ngModel)]="selectAll" (change)="selectAllBrokers()" />
              </div>
            </th>
            <th class="min-w-150px" [class.rtl-th]="translationService.getCurrentLanguage() === 'ar'">
              {{ getTranslatedText('BROKER_NAME') }}
              <i class="fa-solid fa-arrow-down text-dark-blue"
                [class.ms-1]="translationService.getCurrentLanguage() !== 'ar'"
                [class.me-1]="translationService.getCurrentLanguage() === 'ar'"></i>
            </th>
            <th class="min-w-150px" [class.rtl-th]="translationService.getCurrentLanguage() === 'ar'">
              {{ getTranslatedText('ACCOUNT_TYPE') }}
              <i class="fa-solid fa-arrow-down text-dark-blue"
                [class.ms-1]="translationService.getCurrentLanguage() !== 'ar'"
                [class.me-1]="translationService.getCurrentLanguage() === 'ar'"></i>
            </th>
            <th class="min-w-100px" [class.rtl-th]="translationService.getCurrentLanguage() === 'ar'">{{
              getTranslatedText('TYPE') }}</th>
            <th class="min-w-100px" [class.rtl-th]="translationService.getCurrentLanguage() === 'ar'">
              {{ getTranslatedText('AREA') }}
              <i class="fa-solid fa-arrow-down text-dark-blue"
                [class.ms-1]="translationService.getCurrentLanguage() !== 'ar'"
                [class.me-1]="translationService.getCurrentLanguage() === 'ar'"></i>
            </th>
            <th class="min-w-150px" [class.rtl-th]="translationService.getCurrentLanguage() === 'ar'">
              {{ getTranslatedText('SPECIALIZATION') }}
              <i class="fa-solid fa-arrow-down text-dark-blue"
                [class.ms-1]="translationService.getCurrentLanguage() !== 'ar'"
                [class.me-1]="translationService.getCurrentLanguage() === 'ar'"></i>
            </th>
            <!-- <th class="min-w-100px rounded-end" [class.text-end]="translationService.getCurrentLanguage() !== 'ar'"
              [class.text-start]="translationService.getCurrentLanguage() === 'ar'"
              [class.pe-4]="translationService.getCurrentLanguage() !== 'ar'"
              [class.ps-4]="translationService.getCurrentLanguage() === 'ar'"
              [class.rtl-th]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('ACTIONS') }}</th> -->
          </tr>
        </thead>

        <tbody>
          <tr *ngFor="let broker of AssignBroker; trackBy: trackById">
            <td class="ps-4">
              <div class="form-check form-check-sm form-check-custom form-check-solid">
                <input class="form-check-input widget-13-check" type="checkbox" value="1" [(ngModel)]="broker.selected"
                  (change)="updateSelectAllStatus()" />
              </div>
            </td>

            <td>
              <div class="d-flex align-items-center">
                <div class="symbol symbol-45px me-5">
                  <img [src]="broker.image" alt="" class="rounded-circle" />
                </div>
                <div class="d-flex justify-content-start flex-column">
                  <a class="text-gray-900 fw-bold text-hover-dark-blue fs-5">
                    {{ broker.fullName }}
                  </a>
                </div>
              </div>
            </td>

            <td [class.rtl-td]="translationService.getCurrentLanguage() === 'ar'">
              <span *ngIf="broker.accountType === 'Golden Account'" class="badge badge-light-warning fs-5"
                [class.rtl-badge]="translationService.getCurrentLanguage() === 'ar'">
                {{ getTranslatedText('GOLDEN_ACCOUNT') }}
              </span>
              <span *ngIf="broker.accountType === 'Silver Account'" class="badge badge-light-primary fs-5"
                [class.rtl-badge]="translationService.getCurrentLanguage() === 'ar'">
                {{ getTranslatedText('SILVER_ACCOUNT') }}
              </span>
              <span *ngIf="broker.accountType === 'Bronze Account'" class="badge badge-light-danger fs-5"
                [class.rtl-badge]="translationService.getCurrentLanguage() === 'ar'">
                {{ getTranslatedText('BRONZE_ACCOUNT') }}
              </span>
              <span *ngIf="broker.accountType === 'Free'" class="badge text-dark bg-light fs-5"
                [class.rtl-badge]="translationService.getCurrentLanguage() === 'ar'">
                {{ getTranslatedText('FREE') }}
              </span>
            </td>

            <td [class.rtl-td]="translationService.getCurrentLanguage() === 'ar'">
              <span *ngIf="broker.type === 'Real Estate Brokage Company'" class="badge badge-light-info fs-5"
                [class.rtl-badge]="translationService.getCurrentLanguage() === 'ar'">
                {{ getTranslatedText('REAL_ESTATE_BROKAGE_COMPANY') }}
              </span>
              <span *ngIf="broker.type === 'Independent'" class="badge badge-light-success fs-5"
                [class.rtl-badge]="translationService.getCurrentLanguage() === 'ar'">
                {{ getTranslatedText('INDEPENDENT') }}
              </span>
            </td>

            <td>
              <div *ngFor="let area of broker.areas;">
                <span class="badge badge-dark-blue fs-7 m-1">{{ area.nameEn }} | {{ area.nameAr }}</span>
              </div>
            </td>

            <td>
              <div *ngFor="let specialization of broker.specializations;">
                <span class="badge badge-dark-blue fs-7 m-1">{{ specialization.specialization }}</span>
              </div>
            </td>

            <!-- <td class="text-end pe-4">
              <button type="button" class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary"
                data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end" data-kt-menu-flip="top-end">
                <i class="fa-solid fa-ellipsis-vertical"></i>
              </button>
              <app-assignto-brokers-action-menu></app-assignto-brokers-action-menu>
            </td> -->
          </tr>


        </tbody>
      </table>
    </div>

    <!-- Assign Button in Center -->
    <div class="text-center mt-4 mb-4" [class.rtl-center]="translationService.getCurrentLanguage() === 'ar'">
      <button class="btn btn-light-dark-blue fw-bolder px-5 py-3"
        [class.rtl-button]="translationService.getCurrentLanguage() === 'ar'" (click)="assignSelectedBrokers()"
        [disabled]="selectedCount === 0" routerLink="/requests/sent">
        {{ getTranslatedText('ASSIGN_TO_BROKER') }} ({{ selectedCount }})
      </button>
    </div>
  </div>
</div>