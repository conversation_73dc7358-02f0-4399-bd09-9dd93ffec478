// RTL Support for Header Layout
.rtl-header {
  .d-flex {
    flex-direction: row-reverse !important;

    &.justify-content-between {
      justify-content: space-between !important;
    }
  }
}

// RTL Title styling
.rtl-title {
  text-align: right !important;
  margin-right: 0 !important;
  margin-left: 0.25rem !important;
  font-family: 'Noto <PERSON> Arabic', sans-serif !important;
  font-weight: 800 !important;
}

// RTL Search form styling
.rtl-search {
  .form-control {
    text-align: right !important;
    padding-right: 1rem !important;
    padding-left: 2.5rem !important;
  }

  app-keenicon {
    right: auto !important;
    left: 12px !important;
  }
}

.rtl-input {
  text-align: right !important;
  font-family: 'Hacen Liner Screen', sans-serif !important;
}

// RTL Button styling
.rtl-button {
  font-family: 'Hacen Liner Screen', sans-serif !important;
  font-weight: 600 !important;

  i {
    margin-right: 0.5rem !important;
    margin-left: 0 !important;
  }
}

// RTL Center for assign button
.rtl-center {
  text-align: center !important;
}

// Enhanced button styling
.btn-light-dark-blue {
  border-radius: 8px !important;
  font-size: 0.95rem !important;
  transition: all 0.3s ease !important;
  padding: 0.5rem 1rem !important;

  &:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  }

  &:disabled {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: none !important;
  }
}

// Header layout improvements
.card-body {
  .d-flex.flex-wrap.flex-sm-nowrap {
    gap: 1rem;

    .flex-grow-1 {
      .d-flex.justify-content-between {
        gap: 1rem;
        align-items: center !important;

        // Title section
        .d-flex.my-4:first-child {
          margin: 0 !important;

          h1 {
            margin: 0 !important;
            line-height: 1.2 !important;
          }
        }

        // Search section
        .d-flex.my-4:nth-child(2) {
          margin: 0 !important;

          form {
            margin-bottom: 0 !important;
          }
        }

        // Buttons section
        .d-flex.my-4:last-child {
          margin: 0 !important;
          gap: 0.5rem;

          .btn {
            white-space: nowrap;
          }
        }
      }
    }
  }
}

// Arabic font support
:host-context(html[lang="ar"]) {
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Noto Kufi Arabic', sans-serif !important;
    font-weight: 800 !important;
  }

  .btn {
    font-family: 'Hacen Liner Screen', sans-serif !important;
    font-weight: 600 !important;
  }

  .form-control {
    font-family: 'Hacen Liner Screen', sans-serif !important;
  }

  // Header specific adjustments for Arabic
  .rtl-header {
    .d-flex.justify-content-between {
      flex-wrap: wrap !important;
      gap: 1rem !important;

      > div {
        flex: 0 0 auto !important;
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .card-body {
    .d-flex.flex-wrap.flex-sm-nowrap {
      .flex-grow-1 {
        .d-flex.justify-content-between {
          flex-direction: column !important;
          align-items: stretch !important;
          gap: 1rem !important;

          .d-flex.my-4 {
            justify-content: center !important;

            &:last-child {
              justify-content: center !important;

              .btn {
                flex: 1 !important;
                max-width: 120px !important;
              }
            }
          }
        }
      }
    }
  }

  // RTL mobile adjustments
  .rtl-header {
    .d-flex.justify-content-between {
      > div {
        width: 100% !important;
        justify-content: center !important;
      }
    }
  }
}
