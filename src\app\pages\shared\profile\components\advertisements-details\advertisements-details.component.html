<!-- profile details -->

<div class="card mb-5 mb-xl-10">
  <div class="card-header border-0 bg-light-dark-blue" role="button" data-bs-toggle="collapse"
    data-bs-target="#kt_account_ads_details" aria-expanded="true" aria-controls="kt_account_ads_details">
    <div class="card-title m-0">
      <h3 class="fw-bolder m-0">{{ 'PROFILE.ADVERTISEMENTS_DETAILS.TITLE' | translate }}</h3>
    </div>
  </div>
  <div id="kt_account_ads_details" class="collapse show">
    <form novalidate="" class="form">
      <div class="card-body border-top p-9">
        <div class="row mb-6">
          <label class="col-lg-4 col-form-label fw-bold fs-6">
            <span>{{ 'PROFILE.ADVERTISEMENTS_DETAILS.SPECIALIZATIONS' | translate }} </span>
          </label>
          <div class="col-lg-8 fv-row">
            <div class="row">
              <div class="col-md-9"></div>
              <div class="col-md-3">
                <div class="align-items-end text-end">
                  <button class="btn btn-light-dark-blue btn-sm" (click)="openSpecializationModal()">
                    <i class="fa-solid fa-arrow-right"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- <div class="row mb-6">
  <label class="col-lg-4 col-form-label fw-bold fs-6">
    <span>Specializations</span>
  </label>
  <div class="col-lg-8 fv-row">
    <div class="row">
      <div class="col-md-9">
        <ng-container *ngIf="user?.specializationScopes?.length; else noSpecializations">
          <div class="badge bg-secondary text-white me-2 mb-1" *ngFor="let s of user.specializationScopes">
            {{ specializationDisplayMap[s.specialization] }}
          </div>
        </ng-container>
        <ng-template #noSpecializations>
          <span class="text-muted">No specializations selected</span>
        </ng-template>
      </div>

      <div class="col-md-3 text-end">
        <button class="btn btn-light-dark-blue btn-sm" (click)="openSpecializationModal()">
          <i class="fa-solid fa-arrow-right"></i>
        </button>
      </div>
    </div>
  </div>
</div> -->
        <div class="row mb-6">
          <label class="col-lg-4 col-form-label fw-bold fs-6">
            <span>{{ 'PROFILE.ADVERTISEMENTS_DETAILS.LOCATIONS' | translate }}</span>
            <i class="ki-duotone ki-information-5 text-danger fs-6 ms-1" data-bs-toggle="tooltip"
              data-bs-placement="top" data-bs-custom-class="red-tooltip"
              [title]="'PROFILE.ADVERTISEMENTS_DETAILS.LOCATIONS_TOOLTIP' | translate">
              <span class="path1"></span>
              <span class="path2"></span>
              <span class="path3"></span>
            </i>
          </label>
          <div class="col-lg-8 fv-row">
            <div class="row">
              <div class="col-md-9 ">
                <div class="d-flex   fw-bold fs-6 mb-4 pe-2 w-25 ">
                  <span *ngFor="let loc of user.areas; let i = index"
                    class="d-flex align-items-center me-5 mb-2 fw-bolder py-4 px-5 fs-6 badge badge-light-dark-blue text-dark-blue">
                    {{ getLocalizedAreaName(loc) }}
                    <button class="btn btn-icon btn-light btn-active-light-danger rounded-circle ms-1" type="button"
                      (click)="
                        removeLocation(i, loc.id); $event.stopPropagation()
                      " title="Remove">
                      <i class="fa-solid fa-times"></i>
                    </button>
                  </span>
                </div>
              </div>
              <div class="col-md-3">
                <div class="align-items-end text-end">
                  <button class="btn btn-light-dark-blue btn-sm" (click)="openLocationModal()">
                    <i class="fa-solid fa-plus"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>

<!-- ****************************************************************** -->

<!-- Modal for adding locations -->
<div class="modal fade" id="addLocationModal" tabindex="-1" aria-labelledby="addLocationModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header bg-light-primary">
        <h5 class="modal-title fw-bold text-dark-blue" id="addLocationModalLabel">
          <i class="fas fa-map-marker-alt me-2"></i> {{ 'PROFILE.ADVERTISEMENTS_DETAILS.ADD_NEW_LOCATION' | translate }}
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body" [formGroup]="Form">
        <div class="card mb-4">
          <div class="card-body p-4">
            <!-- City Selection -->
            <div class="mb-4">
              <label class="form-label fw-bold text-start d-block fs-6">{{ 'PROFILE.ADVERTISEMENTS_DETAILS.CITY' |
                translate }}</label>

              <!-- Loading indicator -->
              <div *ngIf="isLoadingCities" class="d-flex align-items-center text-primary mb-2">
                <i class="fas fa-spinner fa-spin me-2"></i> {{ 'PROFILE.ADVERTISEMENTS_DETAILS.LOADING_CITIES' |
                translate }}
              </div>

              <!-- Debug info -->
              <div *ngIf="!isLoadingCities && cities.length === 0" class="alert alert-warning py-2 mb-2">
                <i class="fas fa-exclamation-triangle me-2"></i> {{ 'PROFILE.ADVERTISEMENTS_DETAILS.NO_CITIES' |
                translate }}
              </div>

              <div class="dropdown bg-secondary">
                <button
                  class="btn btn-outline-primary w-100 text-start d-flex justify-content-between align-items-center py-2"
                  type="button" id="cityDropdownStep1" data-bs-toggle="dropdown" aria-expanded="false"
                  [disabled]="isLoadingCities">
                  <span>
                    <ng-container *ngIf="isLoadingCities">
                      <i class="fas fa-spinner fa-spin me-2"></i> {{ 'PROFILE.ADVERTISEMENTS_DETAILS.LOADING_CITIES' |
                      translate }}
                    </ng-container>
                    <ng-container *ngIf="!isLoadingCities">
                      {{ selectedCityName || ('PROFILE.ADVERTISEMENTS_DETAILS.SELECT_CITY' | translate) }}
                    </ng-container>
                  </span>
                  <i class="fas fa-chevron-down"></i>
                </button>

                <ul class="dropdown-menu w-100 shadow-sm" aria-labelledby="cityDropdownStep1" style="
                    max-height: 250px;
                    overflow-y: auto;
                    position: absolute;
                    z-index: 1000;
                  ">
                  <!-- Loading indicator -->
                  <li *ngIf="isLoadingCities" class="dropdown-item disabled text-primary">
                    <i class="fas fa-spinner fa-spin me-2"></i> {{ 'PROFILE.ADVERTISEMENTS_DETAILS.LOADING_CITIES' |
                    translate }}
                  </li>

                  <!-- Debug info -->
                  <li *ngIf="!isLoadingCities" class="dropdown-item disabled small text-muted">
                    {{ 'PROFILE.ADVERTISEMENTS_DETAILS.AVAILABLE_CITIES' | translate }}: {{ cities.length }}
                  </li>

                  <ng-container *ngIf="cities && cities.length > 0; else noCities">
                    <li *ngFor="let city of cities" style="cursor: pointer">
                      <a class="dropdown-item text-start py-2" (click)="selectCity(city.id, city.name_en)">
                        {{ getLocalizedCityName(city) }}
                      </a>
                    </li>
                  </ng-container>

                  <ng-template #noCities>
                    <li>
                      <a class="dropdown-item text-start disabled py-2">
                        {{ 'PROFILE.ADVERTISEMENTS_DETAILS.NO_CITIES_AVAILABLE' | translate }}
                      </a>
                    </li>
                  </ng-template>
                </ul>
              </div>
            </div>

            <!-- Area Selection -->
            <div class="mb-2">
              <label class="form-label fw-bold text-start d-block fs-6">{{ 'PROFILE.ADVERTISEMENTS_DETAILS.AREA' |
                translate }}</label>

              <!-- Show message when no city is selected -->
              <div *ngIf="!selectedCityId" class="alert alert-warning py-2 mb-2">
                <i class="fas fa-exclamation-triangle me-2"></i> {{ 'PROFILE.ADVERTISEMENTS_DETAILS.SELECT_CITY_FIRST' |
                translate }}
              </div>

              <div class="dropdown bg-secondary">
                <button
                  class="btn btn-outline-primary w-100 text-start d-flex justify-content-between align-items-center py-2"
                  type="button" id="areaDropdownStep1" data-bs-toggle="dropdown" aria-expanded="false"
                  [disabled]="!selectedCityId">
                  <span>{{ selectedAreaName || ('PROFILE.ADVERTISEMENTS_DETAILS.SELECT_AREA' | translate) }}</span>
                  <i class="fas fa-chevron-down"></i>
                </button>

                <ul class="dropdown-menu w-100 shadow-sm" aria-labelledby="areaDropdownStep1" style="
                    max-height: 250px;
                    overflow-y: auto;
                    position: absolute;
                    z-index: 1000;
                  ">
                  <!-- Show message when no city is selected -->
                  <li *ngIf="!selectedCityId" class="dropdown-item disabled text-danger">
                    <i class="fas fa-exclamation-circle me-2"></i> {{
                    'PROFILE.ADVERTISEMENTS_DETAILS.NO_AREAS_AVAILABLE' | translate }}
                  </li>

                  <!-- Show areas when available -->
                  <ng-container *ngIf="selectedCityId && areas.length > 0">
                    <li *ngFor="let area of areas">
                      <a class="dropdown-item text-start py-2" (click)="selectArea(area.id, area.name_en)">
                        {{ getLocalizedAreaName(area) }}
                      </a>
                    </li>
                  </ng-container>

                  <!-- Show message when city is selected but no areas are available -->
                  <li *ngIf="selectedCityId && areas.length === 0" class="dropdown-item disabled">
                    <i class="fas fa-info-circle me-2"></i> {{ 'PROFILE.ADVERTISEMENTS_DETAILS.NO_AREAS_FOR_CITY' |
                    translate }}
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-light-dark px-4" data-bs-dismiss="modal">
          {{ 'PROFILE.SIGN_IN_METHOD.CANCEL' | translate }}
        </button>
        <button type="button" class="btn btn-primary px-4" (click)="saveLocation()">
          <i class="fa-solid fa-save me-2"></i> {{ 'PROFILE.ADVERTISEMENTS_DETAILS.SAVE_LOCATION' | translate }}
        </button>
      </div>
    </div>
  </div>
</div>

<!-- ****************************************************************** -->

<!-- Modal for adding specializations -->
<div class="modal fade" id="addSpecializationModal" tabindex="-1" aria-labelledby="addSpecializationModalLabel"
  aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content">
      <div class="modal-header bg-light-primary">
        <h5 class="modal-title fw-bold" id="addSpecializationModalLabel">
          {{ 'PROFILE.ADVERTISEMENTS_DETAILS.SELECT_SPECIALIZATIONS' | translate }}
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="mb-4">
          <h6 class="form-label fw-bold mb-3">
            {{ 'PROFILE.ADVERTISEMENTS_DETAILS.CHOOSE_SPECIALIZATION_TREE' | translate }}
          </h6>

          <div class="specialization-tree-container">
            <!-- Search box -->
            <div class="mb-4">
              <div class="input-group">
                <span class="input-group-text bg-light">
                  <i class="fa-solid fa-search"></i>
                </span>
                <input type="text" class="form-control form-control-solid"
                  [placeholder]="'PROFILE.ADVERTISEMENTS_DETAILS.SEARCH_SPECIALIZATIONS' | translate"
                  [(ngModel)]="searchTerm" name="searchTerm" />
                <button *ngIf="searchTerm" class="btn btn-light-primary" type="button">
                  <i class="fa-solid fa-times"></i>
                </button>
              </div>
            </div>

            <!-- Simplified 2-level Tree view -->
            <div class="specialization-tree">
              <div *ngFor="let scope of staticScopes" class="tree-node">
                <!-- Scope -->
                <div class="d-flex align-items-center py-2" style="cursor: pointer"
                  (click)="scope.expanded = !scope.expanded">
                  <span class="me-2" (click)="
                      $event.stopPropagation(); scope.expanded = !scope.expanded
                    ">
                    <i [class]="
                        scope.expanded
                          ? 'fas fa-chevron-down'
                          : 'fas fa-chevron-right'
                      "></i>
                  </span>

                  <div class="form-check me-2" (click)="$event.stopPropagation()">
                    <input class="form-check-input" type="checkbox" [checked]="hasScope(scope.specialization_scope)" />
                  </div>

                  <div class="fw-bold fs-5">
                    {{ scope.specialization_scope }}
                  </div>
                </div>

                <div *ngIf="scope.expanded" class="ms-5 ps-3 py-1">
                  <div *ngFor="let type of scope.specializations" class="d-flex align-items-center mb-1">
                    <div class="form-check me-2">
                      <input class="form-check-input" type="checkbox" [checked]="
                          hasSpecialization(scope.specialization_scope, type)
                        " (change)="
                          onScopeChange(
                            scope.specialization_scope,
                            type,
                            $event
                          )
                        " />
                    </div>
                    <div class="fw-semibold text-primary"></div>
                    <div class="ms-3 text-muted">
                      <ng-container *ngIf="
                          type.includes('Factories') ||
                          type.includes('Warehouses')
                        ">
                        <span class="badge badge-light-info me-2">{{ 'PROFILE.ADVERTISEMENTS_DETAILS.INDUSTRIAL' |
                          translate }}</span>
                      </ng-container>
                      <ng-container *ngIf="
                          type.includes('Administrative') ||
                          type.includes('Commercial')
                        ">
                        <span class="badge badge-light-info me-2">{{ 'PROFILE.ADVERTISEMENTS_DETAILS.COMMERCIAL' |
                          translate }}</span>
                      </ng-container>
                      <ng-container *ngIf="
                          type.includes('Apartment') ||
                          type.includes('Duplex') ||
                          type.includes('Penthouse') ||
                          type.includes('Townhouse')
                        ">
                        <span class="badge badge-light-info me-2">{{ 'PROFILE.ADVERTISEMENTS_DETAILS.RESIDENTIAL' |
                          translate }}</span>
                      </ng-container>
                      <ng-container *ngIf="
                          type.includes('Sakan Misr') ||
                          type.includes('Dar Misr') ||
                          type.includes('Ganat Misr')
                        ">
                        <span class="badge badge-light-info me-2">{{ 'PROFILE.ADVERTISEMENTS_DETAILS.NATIONAL_PROJECT' |
                          translate }}</span>
                      </ng-container>
                      <!-- {{ type }} -->
                      {{ specializationDisplayMap[type] || type }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="form-text text-muted mt-2">
            {{ 'PROFILE.ADVERTISEMENTS_DETAILS.SELECT_SCOPES_TYPES' | translate }}
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-light" data-bs-dismiss="modal">
          {{ 'PROFILE.SIGN_IN_METHOD.CANCEL' | translate }}
        </button>
        <button type="button" class="btn btn-primary" [disabled]="!formChanged" (click)="saveChanges()">
          <i class="fa-solid fa-save me-1"></i> {{ 'PROFILE.ADVERTISEMENTS_DETAILS.SAVE' | translate }}
        </button>
      </div>
    </div>
  </div>
</div>