// Override global flex-wrap rules for specialization badges
:host {
  .d-flex.flex-wrap {
    direction: inherit !important;
    gap: 0.5rem !important;
    justify-content: flex-start !important;
  }
}

// Enhanced Arabic RTL Support for Broker Title
:host-context(html[lang="ar"]) {
  // Override global flex-wrap rules specifically for Arabic
  .d-flex.flex-wrap {
    direction: rtl !important;
    gap: 0.5rem !important;
    justify-content: flex-start !important;
  }
  .card {
    direction: rtl !important;
    text-align: right !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
    border: 1px solid #e5e7eb !important;
    margin-bottom: 1.5rem !important;
  }

  .card-body {
    padding: 1.5rem !important;

    .row.align-items-center {
      gap: 1rem !important;

      .col-12.col-sm-auto {
        text-align: center !important;

        .profile-image-arabic {
          width: 90px !important;
          height: 90px !important;
          border-radius: 12px !important;
          overflow: hidden !important;
          border: 3px solid #e5e7eb !important;
          margin: 0 auto !important;

          img {
            width: 100% !important;
            height: 100% !important;
            object-fit: cover !important;
          }
        }
      }

      .col {
        .row.justify-content-between {
          .col-12.col-lg-9 {
            .d-flex.align-items-center.flex-wrap {
              justify-content: flex-start !important;
              gap: 0.5rem !important;
              margin-bottom: 1rem !important;

              .text-gray-800.fs-2.fw-bolder {
                font-family: 'Noto Kufi Arabic', sans-serif !important;
                font-size: 1.8rem !important;
                font-weight: 800 !important;
                color: #1e3a8a !important;
                margin: 0 !important;
              }

              a {
                font-family: 'Noto Kufi Arabic', sans-serif !important;
                font-size: 1.8rem !important;
                font-weight: 700 !important;
                color: #1f2937 !important;
                text-decoration: none !important;

                &:hover {
                  color: #1e3a8a !important;
                }
              }

              .badge {
                font-family: 'Hacen Liner Screen', sans-serif !important;
                font-size: 0.8rem !important;
                font-weight: 600 !important;
                padding: 0.4rem 0.8rem !important;
                border-radius: 6px !important;
                margin-right: 0.5rem !important;
                margin-left: 0 !important;
              }
            }

            .d-flex.flex-wrap.fw-bold.fs-6 {
              gap: 1rem !important;
              margin-bottom: 1rem !important;

              .d-flex.align-items-center {
                gap: 0.5rem !important;

                app-keenicon {
                  color: #1e3a8a !important;
                }

                span, a {
                  font-family: 'Hacen Liner Screen', sans-serif !important;
                  font-size: 0.95rem !important;
                  color: #6b7280 !important;

                  &:hover {
                    color: #1e3a8a !important;
                  }
                }
              }
            }
          }

          .col-lg-auto {
            &.button-container-arabic {
              text-align: left !important;
              display: flex !important;
              justify-content: flex-start !important;
              align-items: flex-start !important;
              margin-top: 0.5rem !important;

              .btn {
                font-family: 'Hacen Liner Screen', sans-serif !important;
                font-weight: 600 !important;
                border-radius: 8px !important;
                padding: 0.6rem 1.2rem !important;
                white-space: nowrap !important;
                transition: all 0.3s ease !important;
                margin: 0 !important;

                &.btn-dark-blue {
                  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%) !important;
                  border: none !important;
                  color: white !important;

                  &:hover {
                    background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%) !important;
                    transform: translateY(-1px) !important;
                    box-shadow: 0 4px 12px rgba(30, 58, 138, 0.3) !important;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  // Text alignment fixes
  .text-lg-end {
    text-align: left !important;
  }

  .me-1, .me-5 {
    margin-left: 0.25rem !important;
    margin-right: 0 !important;
  }

  .ms-2 {
    margin-right: 0.5rem !important;
    margin-left: 0 !important;
  }
}

// Default styles for Arabic layout
.profile-image-arabic {
  width: 90px;
  height: 90px;
  border-radius: 12px;
  overflow: hidden;
  border: 3px solid #e5e7eb;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.button-container-arabic {
  text-align: left;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;

  .btn {
    margin: 0;
    font-family: 'Hacen Liner Screen', sans-serif;
    font-weight: 600;
    border-radius: 8px;
    padding: 0.6rem 1.2rem;
    transition: all 0.3s ease;
  }
}

// Enhanced card styling
.card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }
}
